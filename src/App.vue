<template>
  <div class="app-container">
    <div class="left-panel" :style="{ width: leftPanelWidth + 'px' }">
      <!-- 左侧面板内容可以在这里添加 -->
    </div>
    <div
      class="resize-handle"
      @mousedown="startResize"
      @touchstart="startResize"
    >
      <div class="resize-indicator"></div>
    </div>
    <iframe
      class="dify-frame"
      :src="resolvedUrl"
      :style="{ width: iframeWidth + 'px' }"
      frameborder="0"
      allow="clipboard-write; microphone; camera;"
    ></iframe>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'

function getQueryParam(name) {
  const url = new URL(window.location.href)
  return url.searchParams.get(name)
}

// 优先读取 URL query: ?difyUrl=...，否则读取环境变量 VITE_DIFY_URL
const resolvedUrl = computed(() => {
  const q = getQueryParam('difyUrl')
  return q || import.meta.env.VITE_DIFY_URL || ''
})

// 拖拽调节宽度相关
const leftPanelWidth = ref(300) // 默认左侧面板宽度
const isResizing = ref(false)

const iframeWidth = computed(() => {
  return window.innerWidth - leftPanelWidth.value - 8 // 8px 是拖拽手柄的宽度
})

// 开始拖拽
const startResize = (e) => {
  isResizing.value = true
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.addEventListener('touchmove', handleResize)
  document.addEventListener('touchend', stopResize)

  // 防止选中文本
  e.preventDefault()
  document.body.style.userSelect = 'none'
}

// 处理拖拽
const handleResize = (e) => {
  if (!isResizing.value) return

  const clientX = e.clientX || (e.touches && e.touches[0].clientX)
  if (clientX) {
    const newLeftWidth = Math.max(100, Math.min(window.innerWidth - 200, clientX))
    leftPanelWidth.value = newLeftWidth
  }
}

// 停止拖拽
const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.removeEventListener('touchmove', handleResize)
  document.removeEventListener('touchend', stopResize)
  document.body.style.userSelect = ''
}

// 窗口大小改变时调整
const handleWindowResize = () => {
  if (leftPanelWidth.value > window.innerWidth - 200) {
    leftPanelWidth.value = window.innerWidth - 200
  }
}

onMounted(() => {
  // 初始化左侧面板宽度为窗口宽度的30%
  leftPanelWidth.value = Math.min(300, window.innerWidth * 0.3)
  window.addEventListener('resize', handleWindowResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize)
})
</script>

<style scoped>
.app-container {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: stretch;
  overflow: hidden;
}

.left-panel {
  height: 100%;
  background: #fafafa;
  border-right: 1px solid #ddd;
  min-width: 100px;
  flex-shrink: 0;
}

.dify-frame {
  border: none;
  height: 100%;
  flex: 1;
  min-width: 200px;
}

.resize-handle {
  width: 8px;
  height: 100%;
  background: #f0f0f0;
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
  cursor: col-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
  transition: background-color 0.2s ease;
}

.resize-handle:hover {
  background: #e0e0e0;
}

.resize-handle:active {
  background: #d0d0d0;
}

.resize-indicator {
  width: 3px;
  height: 40px;
  background: #999;
  border-radius: 2px;
  position: relative;
}

.resize-indicator::before,
.resize-indicator::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 40px;
  background: #999;
  border-radius: 2px;
}

.resize-indicator::before {
  left: -6px;
}

.resize-indicator::after {
  left: 6px;
}

.right-panel {
  flex: 1;
  height: 100%;
  background: #fafafa;
  border-left: 1px solid #ddd;
  min-width: 100px;
}

/* 拖拽时的全局样式 */
body.resizing {
  cursor: col-resize !important;
  user-select: none !important;
}

body.resizing * {
  cursor: col-resize !important;
  user-select: none !important;
}
</style>

